{"flutter": {"platforms": {"android": {"default": {"projectId": "YOUR_PROJECT_ID_HERE", "appId": "YOUR_ANDROID_APP_ID_HERE", "fileOutput": "android/app/google-services.json"}}, "dart": {"lib/firebase_options.dart": {"projectId": "YOUR_PROJECT_ID_HERE", "configurations": {"android": "YOUR_ANDROID_APP_ID_HERE", "ios": "YOUR_IOS_APP_ID_HERE"}}}}}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"]}], "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}}